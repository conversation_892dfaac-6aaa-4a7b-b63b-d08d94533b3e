"use client"

import Image from "next/image"
import Script from "next/script"
import { useState, useEffect } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { CreditCard, Wallet, ArrowRight, RefreshCw } from "lucide-react"
import { VIPCardButton, MonthlyVIPStripeButton, AnnualVIPStripeButton } from "@/components/payments/vip-card-button"
import { formatCurrency } from "@/lib/pricing-service"

type PaymentMode = 'stripe' | 'xrpl'

export default function UpgradePage() {
  const [paymentMode, setPaymentMode] = useState<PaymentMode>('stripe')
  const [cryptoPrices, setCryptoPrices] = useState<any>(null)
  const [pricesLoading, setPricesLoading] = useState(false)

  const cards = [
    { name: "Monthly VIP Card", description: "Just a taste of fuse?", price: 9.99, type: "monthly", rewards: { referral: "15%", affiliate: "0.5%" },
      cardImage: "https://hebbkx1anhila5yf.public.blob.vercel-storage.com/premium-card-jvnxFn2cckvmHmXc3LEiYxIXgsVH9k.png" },
    { name: "Premium Card", price: 100, type: "annual", rewards: { referral: "20%", affiliate: "1%" },
      cardImage: "https://hebbkx1anhila5yf.public.blob.vercel-storage.com/premium-card-jvnxFn2cckvmHmXc3LEiYxIXgsVH9k.png" },
    { name: "Obsidian Card", description: "Ultimate lifetime access", price: 1500, type: "lifetime", rewards: { referral: "40%", affiliate: "5%" },
      cardImage: "https://hebbkx1anhila5yf.public.blob.vercel-storage.com/obsidian-card-sZV0uG2g9pJ0BiQRLvn14MJIFGvzDn.png" },
    { name: "Gold Card", rewards: { referral: "25%", affiliate: "2%" }, type: "coming-soon", price: 250,
      cardImage: "https://hebbkx1anhila5yf.public.blob.vercel-storage.com/gold-card-bflkHLVolhMSi3L5RexoL9fh7wsqBq.png" },
    { name: "Platinum Card", rewards: { referral: "30%", affiliate: "3%" }, type: "coming-soon", price: 500,
      cardImage: "https://hebbkx1anhila5yf.public.blob.vercel-storage.com/platinum-card-IQekSEB3CMuXes5qUTxZc55ZeKUVOy.png" },
    { name: "Diamond Card", rewards: { referral: "35%", affiliate: "4%" }, type: "coming-soon", price: 1000,
      cardImage: "https://hebbkx1anhila5yf.public.blob.vercel-storage.com/diamond-card-I9uaKzc6KKTRSgl9mrz4SP2Y2myuiF.png" }
  ]



  // Fetch crypto prices for XRPL payments
  const fetchCryptoPrices = async () => {
    try {
      setPricesLoading(true)
      
      // Use the simpler, faster API first
      const response = await fetch('/api/pricing/simple')
      const data = await response.json()
      
      console.log('Pricing API response:', data)
      
      if (data.success && data.cards) {
        setCryptoPrices(data.cards)
        console.log('Set crypto prices:', data.cards)
      } else {
        console.error('Pricing API returned error:', data.error)
        
        // Set fallback prices if API fails
        const fallbackPrices = cards.map(card => ({
          name: card.name,
          usdPrice: card.price,
          xrpPrice: (card.price / 2.4).toFixed(6),
          fusePrice: Math.round(card.price / 0.00172).toLocaleString(),
          rates: {
            xrpUsd: 2.4,
            fuseUsd: 0.00172,
            fuseXrp: 1394.1,
            xrpFuse: 1 / 1394.1
          }
        }))
        setCryptoPrices(fallbackPrices)
      }
    } catch (error) {
      console.error('Failed to fetch crypto prices:', error)
      
      // Set fallback prices on error
      const fallbackPrices = cards.map(card => ({
        name: card.name,
        usdPrice: card.price,
        xrpPrice: (card.price / 2.4).toFixed(6),
        fusePrice: Math.round(card.price / 0.00172).toLocaleString(),
        rates: {
          xrpUsd: 2.8,
          fuseUsd: 0.00172,
          fuseXrp: 1794.1,
          xrpFuse: 1 / 1794.1
        }
      }))
      setCryptoPrices(fallbackPrices)
    } finally {
      setPricesLoading(false)
    }
  }

  // Load prices on component mount and when XRPL mode is selected
  useEffect(() => {
    // Always fetch prices for display, but especially when XRPL mode is selected
    fetchCryptoPrices()
    
    // Update prices every 5 minutes
    const interval = setInterval(fetchCryptoPrices, 5 * 60 * 1000)
    return () => clearInterval(interval)
  }, [])

  // Refresh prices when switching to XRPL mode
  useEffect(() => {
    if (paymentMode === 'xrpl' && !cryptoPrices) {
      fetchCryptoPrices()
    }
  }, [paymentMode, cryptoPrices])

  // Get crypto price for a card
  const getCryptoPrice = (cardName: string, currency: 'xrp' | 'fuse') => {
    if (pricesLoading) return 'Loading...'
    if (!cryptoPrices || cryptoPrices.length === 0) {
      // Show fallback estimates while loading
      const card = cards.find(c => c.name === cardName)
      if (card) {
        // Use rough estimates: $2.40 per XRP, $0.00172 per FUSE
        const xrpEstimate = (card.price / 2.8).toFixed(4)
        const fuseEstimate = Math.round(card.price / 0.00172).toLocaleString()
        return currency === 'xrp' ? 
          `~${xrpEstimate} XRP` : 
          `~${fuseEstimate} FUSE`
      }
      return 'Loading...'
    }
    
    const cardPrice = cryptoPrices.find((c: any) => c.name === cardName)
    if (!cardPrice) return 'N/A'
    
    return currency === 'xrp' ? 
      `${cardPrice.xrpPrice} XRP` : 
      `${cardPrice.fusePrice} FUSE`
  }



  return (
    <>
      {/* Load Stripe script properly */}
      <Script
        src="https://js.stripe.com/v3/buy-button.js"
        strategy="lazyOnload"
      />

      {/* Hero Section with ThePatriot.png */}
      <section className="relative w-full min-h-[60vh] md:min-h-[70vh] bg-gradient-to-b from-[#000814] via-[#001122] to-black overflow-hidden flex items-center justify-center">
        {/* Background Image - ThePatriot.png */}
        <div className="absolute inset-0 z-0">
          <Image
            src="/images/ThePatriot.png"
            alt="The Patriot - American Financial Freedom"
            fill
            className="object-contain object-center"
            priority
            sizes="100vw"
          />
          {/* Subtle overlay to ensure text readability */}
          <div className="absolute inset-0 bg-gradient-to-t from-black/40 via-transparent to-black/20"></div>
        </div>

        {/* Hero Content */}
        <div className="relative z-10 text-center px-4 max-w-4xl mx-auto">
          <h1 className="text-4xl md:text-6xl font-bold mb-4 bg-gradient-to-r from-[#316bff] to-purple-600 bg-clip-text text-transparent drop-shadow-lg">
            VIP Cards
          </h1>
          <p className="text-xl text-white/90 mb-8 drop-shadow-md">Choose a payment method. Upgrade your experience. Card Holders get discounts at all Fuse partner businesses.</p>
        </div>
      </section>

      <div className="py-8">
        <div className="container mx-auto px-4">
          {/* Payment Method Selector */}
          <div className="flex justify-center mb-8">
            <div className="bg-gray-100 rounded-xl p-2 flex gap-2">
              <button
                onClick={() => setPaymentMode('stripe')}
                className={`px-6 py-3 rounded-lg font-medium transition-all flex items-center gap-2 ${
                  paymentMode === 'stripe'
                    ? 'bg-white text-[#3A56FF] shadow-md'
                    : 'text-gray-600 hover:text-gray-800'
                }`}
              >
                <CreditCard className="h-5 w-5" />
                Credit Card (Stripe)
              </button>
              <button
                onClick={() => setPaymentMode('xrpl')}
                className={`px-6 py-3 rounded-lg font-medium transition-all flex items-center gap-2 ${
                  paymentMode === 'xrpl'
                    ? 'bg-white text-[#316bff] shadow-md'
                    : 'text-gray-600 hover:text-gray-800'
                }`}
              >
                <Wallet className="h-5 w-5" />
                Crypto (XRPL)
              </button>
            </div>
          </div>

        <AnimatePresence mode="wait">
          <motion.div
            key={paymentMode}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.3 }}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 max-w-6xl mx-auto"
          >
            {cards.map((card) => (
              <motion.div
                key={card.name}
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.3, delay: 0.1 }}
                className="bg-white rounded-xl shadow-lg overflow-hidden border border-gray-100 hover:shadow-xl transition-all duration-300"
              >
                <div className="p-3">
                  <Image
                    src={card.cardImage}
                    width={400}
                    height={250}
                    alt={card.name}
                    className="w-full aspect-[8/5] object-contain rounded-lg"
                  />
                </div>
                <div className="p-6">
                  <h3 className="font-bold text-xl mb-2">{card.name}</h3>
                  {card.description && <p className="text-sm text-gray-600 mb-4">{card.description}</p>}
                  
                  <div className="bg-gray-50 rounded-lg p-4 mb-4">
                    <div className="text-sm space-y-2">
                      <div className="flex justify-between">
                        <span className="text-gray-600">Referral:</span>
                        <span className="font-bold text-[#3A56FF]">{card.rewards.referral}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Affiliate:</span>
                        <span className="font-bold text-[#3A56FF]">{card.rewards.affiliate}</span>
                      </div>
                    </div>
                  </div>
                  
                  {card.price && (
                    <div className="mb-4">
                      {paymentMode === 'xrpl' ? (
                        <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl p-4 border border-blue-200">
                          <div className="text-center space-y-2">
                            <div className="text-lg font-bold text-[#316bff]">
                              {getCryptoPrice(card.name, 'fuse')}
                            </div>
                            <div className="text-sm text-gray-600">
                              or {getCryptoPrice(card.name, 'xrp')}
                            </div>
                            <div className="text-xs text-gray-500">
                              Live pricing from DEX
                            </div>
                          </div>
                        </div>
                      ) : (
                        <div className="text-center">
                          <div className="text-2xl font-bold text-[#3A56FF]">
                            ${card.price}
                            {card.type === "monthly" && "/mo"}
                            {card.type === "annual" && "/year"}
                          </div>
                          {cryptoPrices && (
                            <div className="text-xs text-gray-500 mt-1">
                              or {getCryptoPrice(card.name, 'fuse')} / {getCryptoPrice(card.name, 'xrp')}
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  )}
                  
                  {/* Payment Buttons */}
                  {paymentMode === 'stripe' ? (
                    card.type === "monthly" && card.price ? (
                      <MonthlyVIPStripeButton className="w-full" />
                    ) : card.type === "annual" && card.price ? (
                      <AnnualVIPStripeButton className="w-full" />
                    ) : card.type === "lifetime" && card.price ? (
                      <VIPCardButton
                        tier="lifetime"
                        variant="stripe-only"
                        className="w-full py-3 bg-[#3A56FF] text-white rounded-lg font-medium hover:bg-[#2A46EF] transition-colors"
                      />
                    ) : card.type === "coming-soon" ? (
                      <button className="w-full py-3 bg-gray-400 text-white rounded-lg font-medium" disabled>
                        Coming Soon
                      </button>
                    ) : null
                  ) : (
                    // Unified Payment Buttons (Crypto + Stripe options)
                    card.price && card.type !== "coming-soon" ? (
                      <VIPCardButton
                        tier={card.type === 'monthly' ? 'monthly' : card.type === 'annual' ? 'annual' : 'lifetime'}
                        className="w-full py-3 bg-gradient-to-r from-[#316bff] to-purple-600 text-white rounded-lg font-medium hover:from-[#2557d6] hover:to-purple-700 transition-all"
                      >
                        <div className="flex items-center justify-center gap-2">
                          <Wallet className="h-4 w-4" />
                          Choose Payment Method
                          <ArrowRight className="h-4 w-4" />
                        </div>
                      </VIPCardButton>
                    ) : (
                      <button className="w-full py-3 bg-gray-400 text-white rounded-lg font-medium" disabled>
                        Coming Soon
                      </button>
                    )
                  )}
                </div>
              </motion.div>
            ))}
          </motion.div>
        </AnimatePresence>
        

      </div>
    </div>

    {/* Tesla Coil Image at Bottom */}
    <section className="w-full py-8 bg-black flex justify-center">
      <div className="max-w-4xl mx-auto px-4">
        <Image
          src="/images 2/TeslaCoil.png"
          alt="Tesla Coil"
          width={500}
          height={300}
          className="w-full h-auto object-contain"
        />
      </div>
    </section>
    </>
  )
}
