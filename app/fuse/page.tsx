"use client"

import { <PERSON><PERSON>eader } from "@/components/page-header"
import { CtaSection } from "@/components/cta-section"
import { FuseVIPCardSection } from "@/components/fuse/fuse-vip-card-section"
import { ArrowRight, Server, Gamepad2, Zap, Gift, Mail, Wallet } from "lucide-react"
import Image from "next/image"
import Link from "next/link"
import { LottieAnimation } from "@/components/lottie-animation"
import { useAuth } from "@/contexts/auth-context"
import { useState } from "react"
import { Button } from "@/components/ui/button"
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import BlackjackGame from "@/components/blackjack-game"
import { SkyBackground } from "@/components/sky-background"
import ThemeToggle from "@/components/theme-toggle"
import { useTheme } from "next-themes"
import { CookieNotice } from "@/components/cookie-notice"
import { TrustlineSetup } from "@/components/fuse/trustline-setup"

export default function FusePage() {
  const { user, profile } = useAuth()
  const [isGameOpen, setIsGameOpen] = useState(false)
  const { theme, setTheme } = useTheme()

  // Check if user is authenticated and has card holder status
  const canPlayGame = user && profile?.is_card_holder === true
  
  // Theme toggle function
  const toggleTheme = () => {
    setTheme(theme === "dark" ? "light" : "dark")
  }
  const fuseWorlds = [
    {
      id: "web-building",
      title: "Web Automation",
      description: "Deploy enterprise-grade websites that scale automatically. AI handles updates, security patches, and performance optimization while you focus on growth.",
      value: "Eliminate 90% of web maintenance costs",
      icon: <Server className="w-8 h-8 text-white" />,
      color: "from-[#FF6B6B] to-[#FF8E8E]",
      pricing: "$999/month"
    },
    {
      id: "app-building",
      title: "App Automation",
      description: "Launch mobile apps without coding teams. Automated testing, deployment, and store submissions handle the technical complexity.",
      value: "Ship apps 10x faster than traditional development",
      icon: <Gamepad2 className="w-8 h-8 text-white" />,
      color: "from-[#4ECDC4] to-[#6FDDDD]",
      pricing: "$1,299/month"
    },
    {
      id: "saas",
      title: "Business Automation",
      description: "Transform operations with intelligent workflows. Automate customer onboarding, billing, support, and analytics without human intervention.",
      value: "Reduce operational overhead by 70%",
      icon: <Gift className="w-8 h-8 text-white" />,
      color: "from-[#FFD93D] to-[#FFE066]",
      pricing: "$1,599/month"
    }
  ]

  return (
    <>
      {/* Animated Sky Background */}
      <SkyBackground />
      
      {/* Theme Toggle - Fixed position, away from mobile menu */}
      <div className="fixed bottom-6 left-6 z-50">
        <ThemeToggle theme={theme as "light" | "dark"} toggleTheme={toggleTheme} />
      </div>
      
      {/* Hero Section - Signal from the Future */}
      <div className="relative min-h-screen flex items-center justify-center px-4 overflow-hidden">
        {/* Aurora Borealis Accent */}
        <div className="absolute inset-0 opacity-20">
          <div className="absolute inset-0 bg-gradient-to-r from-emerald-400 via-cyan-400 to-violet-400 animate-pulse"></div>
          <div className="absolute inset-0 bg-gradient-to-l from-purple-400 via-pink-400 to-red-400 animate-pulse" style={{animationDelay: '2s'}}></div>
          <div className="absolute inset-0 bg-gradient-to-t from-blue-400 via-indigo-400 to-emerald-400 animate-pulse" style={{animationDelay: '4s'}}></div>
        </div>
        
        {/* Geometric Light Structure */}
        <div className="relative z-10 max-w-6xl mx-auto">
          {/* Breathing Container */}
          <div className="relative animate-pulse-slow">
            {/* Central Transmission */}
            <div className="text-center mb-16">
              <div className="font-mono text-sm md:text-base text-white/60 mb-4 tracking-[0.2em]">
                &gt; TRANSMISSION_INTERCEPTED
              </div>
              <h1 className="font-mono text-4xl md:text-7xl font-bold text-white mb-6 tracking-tight">
                FUSE PROJECT
              </h1>
              <div className="font-mono text-lg md:text-xl text-white/80 mb-8 tracking-wide">
                ROADMAP.EXE
              </div>
              <div className="font-mono text-sm text-white/40 mb-12 tracking-[0.15em]">
                we are not from here
              </div>
            </div>
            
            {/* Resistance Message */}
            <div className="bg-red-900/30 backdrop-blur-sm border border-red-500/30 p-8 mb-16 hover:border-red-400/60 transition-all duration-700 group">
              <div className="font-mono text-red-400/80 text-sm mb-4 tracking-wide animate-pulse">
                &gt; RESISTANCE.TRANSMISSION &gt; PRIORITY_ALPHA
              </div>
              <div className="font-mono text-red-100 text-lg md:text-xl mb-4 leading-relaxed">
                The financial matrix is fragmenting. We've identified 74 of 150 target businesses already in our network.
                <br />
                <span className="text-red-300">Time remaining: August 31st, 2024</span>
              </div>
              <div className="font-mono text-red-200/80 text-sm tracking-wide">
                When we reach 150 businesses, the token giveaway protocol activates.
                <br />
                Strategic partnerships will be announced simultaneously.
              </div>
              <div className="w-full bg-red-500/30 h-px mt-4 group-hover:bg-red-400/60 transition-all duration-700"></div>
            </div>

            {/* Roadmap Coordinates */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-16">
              <div className="bg-black/80 backdrop-blur-sm border border-green-500/30 p-8 hover:border-green-400/60 transition-all duration-700 group">
                <div className="font-mono text-green-400/80 text-sm mb-2 tracking-wide">RESISTANCE.PROGRESS</div>
                <div className="font-mono text-3xl md:text-5xl font-bold text-green-300 mb-2">74/150</div>
                <div className="font-mono text-green-100 text-lg">businesses liberated</div>
                <div className="font-mono text-green-400/60 text-xs mt-2">deadline: august 31st</div>
                <div className="w-full bg-green-500/30 h-px mt-4 group-hover:bg-green-400/60 transition-all duration-700"></div>
              </div>
              
              <div className="bg-black/80 backdrop-blur-sm border border-blue-500/30 p-8 hover:border-blue-400/60 transition-all duration-700 group">
                <div className="font-mono text-blue-400/80 text-sm mb-2 tracking-wide">CARD.ACTIVATION</div>
                <div className="font-mono text-3xl md:text-5xl font-bold text-blue-300 mb-2">500</div>
                <div className="font-mono text-blue-100 text-lg">qr-activated cards</div>
                <div className="font-mono text-blue-400/60 text-xs mt-2">triggers: LP dumps + global expansion</div>
                <div className="w-full bg-blue-500/30 h-px mt-4 group-hover:bg-blue-400/60 transition-all duration-700"></div>
              </div>
              
              <div className="bg-black/80 backdrop-blur-sm border border-purple-500/30 p-8 hover:border-purple-400/60 transition-all duration-700 group">
                <div className="font-mono text-purple-400/80 text-sm mb-2 tracking-wide">GIVEAWAY.PROTOCOL</div>
                <div className="font-mono text-xl md:text-2xl font-bold text-purple-300 mb-2">@150</div>
                <div className="font-mono text-purple-100 text-lg">token rain begins</div>
                <div className="font-mono text-purple-400/60 text-xs mt-2">partnerships announced</div>
                <div className="w-full bg-purple-500/30 h-px mt-4 group-hover:bg-purple-400/60 transition-all duration-700"></div>
              </div>
              
              <div className="bg-black/80 backdrop-blur-sm border border-yellow-500/30 p-8 hover:border-yellow-400/60 transition-all duration-700 group">
                <div className="font-mono text-yellow-400/80 text-sm mb-2 tracking-wide">EXPANSION.NODES</div>
                <div className="font-mono text-xl md:text-2xl font-bold text-yellow-300 mb-2">@500</div>
                <div className="font-mono text-yellow-100 text-lg">global infiltration</div>
                <div className="font-mono text-yellow-400/60 text-xs mt-2">other countries unlocked</div>
                <div className="w-full bg-yellow-500/30 h-px mt-4 group-hover:bg-yellow-400/60 transition-all duration-700"></div>
              </div>
            </div>

            {/* The Choice - Blue Pill vs Red Pill */}
            <div className="bg-black/90 backdrop-blur-sm border border-white/10 p-8 mb-16 hover:border-white/30 transition-all duration-700">
              <div className="font-mono text-white/60 text-sm mb-6 tracking-wide text-center">
                &gt; CONSCIOUSNESS.FORK &gt; MAKE_YOUR_CHOICE
              </div>
              <div className="font-mono text-white/80 text-lg md:text-xl mb-8 text-center leading-relaxed">
                The financial matrix offers you two paths.
                <br />
                <span className="text-white/60">Choose wisely. There is no going back.</span>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                {/* Blue Pill - Stay in Matrix */}
                <div className="bg-blue-900/30 border border-blue-500/30 p-6 rounded-lg hover:border-blue-400/60 transition-all duration-500 group">
                  <div className="text-center">
                    <div className="w-16 h-16 bg-blue-500 rounded-full mx-auto mb-4 flex items-center justify-center text-2xl animate-pulse">
                      💊
                    </div>
                    <div className="font-mono text-blue-300 text-xl mb-2">BLUE PILL</div>
                    <div className="font-mono text-blue-100/80 text-sm mb-4">
                      Continue using traditional payment systems.
                      <br />
                      Stay comfortable. Stay controlled.
                    </div>
                    <div className="font-mono text-blue-400/60 text-xs">
                      banking fees • slow transfers • limited access
                    </div>
                  </div>
                </div>

                {/* Red Pill - Join Resistance */}
                <div className="bg-red-900/30 border border-red-500/30 p-6 rounded-lg hover:border-red-400/60 transition-all duration-500 group">
                  <div className="text-center">
                    <div className="w-16 h-16 bg-red-500 rounded-full mx-auto mb-4 flex items-center justify-center text-2xl animate-pulse">
                      💊
                    </div>
                    <div className="font-mono text-red-300 text-xl mb-2">RED PILL</div>
                    <div className="font-mono text-red-100/80 text-sm mb-4">
                      Buy FUSE tokens. Get VIP card.
                      <br />
                      Add your business to our network.
                    </div>
                    <div className="font-mono text-red-400/60 text-xs">
                      financial freedom • instant transfers • VIP access
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Haptic Enhancement Notice */}
            <div className="bg-gradient-to-r from-purple-900/20 to-cyan-900/20 backdrop-blur-sm border border-cyan-500/20 p-6 mb-16 rounded-lg">
              <div className="font-mono text-cyan-400/80 text-sm mb-2 tracking-wide text-center">
                &gt; SENSORY.ENHANCEMENT &gt; PROTOCOL_ACTIVE
              </div>
              <div className="font-mono text-cyan-100/80 text-center text-sm leading-relaxed">
                Advanced haptic feedback systems detected on compatible devices.
                <br />
                <span className="text-cyan-300">Experience the resistance through touch.</span>
              </div>
            </div>
            
            {/* Split Protocol */}
            <div className="bg-black/80 backdrop-blur-sm border border-white/20 p-8 mb-16 hover:border-white/40 transition-all duration-700">
              <div className="font-mono text-white/60 text-sm mb-4 tracking-wide">BUYBACK.MECHANISM &gt; ACTIVE</div>
              <div className="flex items-center justify-center gap-8">
                <div className="text-center">
                  <div className="font-mono text-2xl md:text-3xl font-bold text-white">20%</div>
                  <div className="font-mono text-white/80">VIP</div>
                </div>
                <div className="w-16 h-px bg-white/40"></div>
                <div className="text-center">
                  <div className="font-mono text-2xl md:text-3xl font-bold text-white">80%</div>
                  <div className="font-mono text-white/80">FUSE</div>
                </div>
              </div>
            </div>
            
            {/* Signal Footer */}
            <div className="text-center">
              <div className="font-mono text-white/40 text-sm mb-4 tracking-[0.15em]">
                building the financial mesh network
              </div>
              <div className="font-mono text-white/60 text-xs tracking-[0.2em]">
                that already exists in 2030
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* VIP Games Section - Moved to top for buy pressure */}
      <section className="py-20 bg-black/30 dark:bg-black/50 backdrop-blur-sm text-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <div className="inline-flex items-center gap-2 bg-gradient-to-r from-yellow-500 to-orange-500 text-black px-6 py-2 rounded-full mb-6">
              <Gamepad2 className="w-4 h-4" />
              <span className="font-bold text-sm uppercase tracking-wider">VIP Exclusive Games</span>
            </div>

            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
              <span className="text-yellow-400">Fuse</span> Gaming
            </h2>

            <p className="text-xl text-gray-300 max-w-3xl mx-auto mb-12">
              Exclusive games for VIP card holders. Enjoy our Duck Dealer experience!
            </p>
          </div>

          <div className="max-w-2xl mx-auto text-center">
            {canPlayGame ? (
              <div className="bg-white/10 dark:bg-black/30 backdrop-blur-sm border border-green-500/30 rounded-xl p-8">
                <div className="text-6xl mb-4">🦆</div>
                <h3 className="text-2xl font-bold text-yellow-300 mb-4">
                  Welcome, VIP Card Holder!
                </h3>
                <p className="text-gray-300 mb-6">
                  You have access to our exclusive Fuse 21 game. Choose your chips and play against our charming duck dealer!
                </p>
                <Button
                  onClick={() => setIsGameOpen(true)}
                  className="bg-gradient-to-r from-yellow-500 to-orange-500 hover:from-yellow-600 hover:to-orange-600 text-black font-bold text-lg px-8 py-4 rounded-xl shadow-lg transform hover:scale-105 transition-all duration-300"
                >
                  <Gamepad2 className="w-5 h-5 mr-2" />
                  Play Fuse 21
                </Button>
              </div>
            ) : (
              <div className="bg-white/10 dark:bg-black/30 backdrop-blur-sm border border-yellow-500/30 rounded-xl p-8">
                <div className="text-6xl mb-4">🔒</div>
                <h3 className="text-2xl font-bold text-yellow-300 mb-4">
                  VIP Access Required
                </h3>
                <p className="text-gray-300 mb-6">
                  Get your VIP card to unlock exclusive Fuse 21 gaming! Play against our duck dealer and have fun.
                </p>
                <div className="space-y-4">
                  {!user ? (
                    <Link href="/login">
                      <Button className="bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white font-bold text-lg px-8 py-4 rounded-xl shadow-lg transform hover:scale-105 transition-all duration-300">
                        Sign In First
                      </Button>
                    </Link>
                  ) : (
                    <Link href="/upgrade">
                      <Button className="bg-gradient-to-r from-yellow-500 to-orange-500 hover:from-yellow-600 hover:to-orange-600 text-black font-bold text-lg px-8 py-4 rounded-xl shadow-lg transform hover:scale-105 transition-all duration-300">
                        <Gift className="w-5 h-5 mr-2" />
                        Get VIP Card
                      </Button>
                    </Link>
                  )}
                  <div className="text-sm text-gray-400">
                    Starting at $9.99/month • Pay with FUSE or XRP
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </section>

      {/* Three Worlds Section */}
      <section className="py-20 bg-white/40 dark:bg-black/40 backdrop-blur-sm">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <div className="inline-flex items-center gap-2 bg-gradient-to-r from-[#316bff] to-[#4f46e5] text-white px-6 py-2 rounded-full mb-6">
              <Zap className="w-4 h-4" />
              <span className="font-bold text-sm uppercase tracking-wider">Automation Systems</span>
            </div>

            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-6">
              Stop Managing. Start <span className="text-[#316bff] dark:text-blue-400">Automating</span>
            </h2>

            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto mb-12">
              Three proven automation systems that handle the work while you capture the growth.
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 max-w-6xl mx-auto mb-16">
            {fuseWorlds.map((world) => (
              <div
                key={world.id}
                className="relative bg-white/80 dark:bg-black/60 backdrop-blur-sm rounded-2xl shadow-lg overflow-hidden border border-gray-100 dark:border-gray-700"
              >
                {/* Gradient Background */}
                <div className={`absolute inset-0 bg-gradient-to-br ${world.color} opacity-5`}></div>

                {/* Content */}
                <div className="relative p-8 text-center">
                  {/* Icon */}
                  <div className="flex justify-center mb-6">
                    <div className={`p-4 rounded-2xl bg-gradient-to-br ${world.color} shadow-lg`}>
                      {world.icon}
                    </div>
                  </div>

                  {/* Title & Description */}
                  <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-3">
                    {world.title}
                  </h3>

                  <p className="text-gray-600 dark:text-gray-300 mb-4 leading-relaxed">
                    {world.description}
                  </p>

                  {/* Value Proposition */}
                  <div className="bg-green-50/80 dark:bg-green-900/40 border border-green-200 dark:border-green-700 rounded-lg p-3 mb-4">
                    <p className="text-green-800 dark:text-green-300 font-semibold text-sm">{world.value}</p>
                  </div>

                  {/* Pricing */}
                  <div className="bg-gray-50/80 dark:bg-gray-800/60 rounded-lg p-4 mb-4">
                    <p className="text-lg font-bold text-[#316bff] dark:text-blue-400">{world.pricing}</p>
                    <p className="text-sm text-gray-500 dark:text-gray-400">NDA Required • Crypto Accepted</p>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Requirements & Contact */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto">
            {/* NDA Requirement */}
            <div className="bg-amber-50/80 dark:bg-amber-900/40 border border-amber-200 dark:border-amber-700 rounded-xl p-6 text-center backdrop-blur-sm">
              <div className="text-2xl mb-3">⚠️</div>
              <h4 className="font-bold text-amber-800 dark:text-amber-300 mb-2">NDA Required</h4>
              <p className="text-amber-700 dark:text-amber-400 text-sm">
                All deployments require signed confidentiality agreement
              </p>
            </div>

            {/* Payment Options */}
            <div className="bg-green-50/80 dark:bg-green-900/40 border border-green-200 dark:border-green-700 rounded-xl p-6 text-center backdrop-blur-sm">
              <div className="text-2xl mb-3">💰</div>
              <h4 className="font-bold text-green-800 dark:text-green-300 mb-2">Crypto Payments</h4>
              <p className="text-green-700 dark:text-green-400 text-sm">
                FUSE tokens accepted • All crypto after scionX integration
              </p>
            </div>

            {/* Contact */}
            <div className="bg-blue-50/80 dark:bg-blue-900/40 border border-blue-200 dark:border-blue-700 rounded-xl p-6 text-center backdrop-blur-sm">
              <div className="text-2xl mb-3">📧</div>
              <h4 className="font-bold text-blue-800 dark:text-blue-300 mb-2">Get Started</h4>
              <a
                href="mailto:<EMAIL>"
                className="text-blue-700 dark:text-blue-400 text-sm hover:text-blue-900 dark:hover:text-blue-300 font-medium"
              >
                <EMAIL>
              </a>
            </div>
          </div>
        </div>
      </section>



      {/* VIP Card Section */}
      <FuseVIPCardSection />

      {/* Payment & Token Section */}
      <section className="py-16 bg-black/40 dark:bg-black/60 backdrop-blur-sm text-white">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row gap-12 items-center max-w-6xl mx-auto">
            <div className="md:w-1/2">
              <h2 className="text-3xl md:text-4xl font-bold mb-6">
                Pay with <span className="text-[#316bff]">FUSE</span>
              </h2>
              <p className="text-white/80 mb-6 text-lg leading-relaxed">
                FUSE token on XRP Ledger powers these enterprise automation systems. Pay directly with tokens or any cryptocurrency.
              </p>
              <p className="text-white/80 mb-8 text-lg leading-relaxed">
                No traditional payment processing. No lengthy contracts. Just automated deployment and immediate results.
              </p>

              <div className="bg-white/5 backdrop-blur-sm rounded-xl p-6 border border-white/10 mb-8">
                <p className="text-white/80 text-sm mb-4">Trade FUSE:</p>
                <a
                  href="https://xmagnetic.org/dex/FUSE%2Brs2G9J95qwL3yw241JTRdgms2hhcLouVHo_XRP%2BXRP?network=mainnet"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-flex items-center gap-2 bg-gradient-to-r from-[#316bff] to-[#4f46e5] text-white px-6 py-3 rounded-lg font-bold hover:shadow-lg transform hover:scale-105 transition-all duration-300"
                >
                  <Zap className="w-4 h-4" />
                  Magnetic DEX
                  <ArrowRight className="w-4 h-4" />
                </a>
              </div>

              <div className="bg-amber-500/10 backdrop-blur-sm rounded-xl p-4 border border-amber-500/20 mb-8">
                <p className="text-amber-200 text-sm font-medium mb-2">⚠️ First Time Trading FUSE?</p>
                <p className="text-amber-100/80 text-sm">
                  You need to set up a trustline in your XRP wallet before you can hold FUSE tokens.
                  <span className="text-amber-200 font-medium"> Set it up below!</span>
                </p>
              </div>
            </div>
            <div className="md:w-1/2">
              <div className="relative">
                <div className="bg-gradient-to-br from-[#316bff]/10 to-[#4f46e5]/10 rounded-2xl p-8">
                  <Image
                    src="https://hebbkx1anhila5yf.public.blob.vercel-storage.com/fuse-token-cVZqlFSRtwajwj78HxXV8wNGxxBtqg.png"
                    width={400}
                    height={400}
                    alt="FUSE Token"
                    className="mx-auto rounded-lg shadow-lg"
                  />

                  {/* Floating automation badges */}
                  <div className="absolute -top-4 -left-4 bg-gradient-to-r from-[#FF6B6B] to-[#FF8E8E] text-white px-4 py-2 rounded-full text-sm font-bold shadow-lg">
                    Web Automation
                  </div>
                  <div className="absolute -top-4 -right-4 bg-gradient-to-r from-[#4ECDC4] to-[#6FDDDD] text-white px-4 py-2 rounded-full text-sm font-bold shadow-lg">
                    App Automation
                  </div>
                  <div className="absolute -bottom-4 left-1/2 transform -translate-x-1/2 bg-gradient-to-r from-[#FFD93D] to-[#FFE066] text-gray-900 px-4 py-2 rounded-full text-sm font-bold shadow-lg">
                    Business Automation
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* FUSE Trustline Setup Section */}
      <section className="py-16 bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-blue-950/30 dark:to-indigo-950/30">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <div className="inline-flex items-center gap-2 bg-gradient-to-r from-blue-600 to-indigo-600 text-white px-6 py-2 rounded-full mb-6">
              <Wallet className="w-4 h-4" />
              <span className="font-bold text-sm uppercase tracking-wider">Wallet Setup</span>
            </div>
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
              Enable <span className="text-blue-600 dark:text-blue-400">FUSE</span> in Your Wallet
            </h2>
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
              Before you can trade or hold FUSE tokens, you need to set up a trustline in your XRP wallet.
              This is a one-time setup that takes just a few seconds.
            </p>
          </div>

          <div className="max-w-4xl mx-auto">
            <TrustlineSetup />
          </div>
        </div>
      </section>

      {/* Deploy CTA Section */}
      <section className="py-16 bg-white/60 dark:bg-black/40 backdrop-blur-sm">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-6">
            Deploy Your Automation
          </h2>
          <p className="text-xl text-gray-600 dark:text-gray-300 mb-8 max-w-2xl mx-auto">
            Contact us to discuss your automation needs. NDA required for all deployments.
          </p>

          <div className="flex flex-col sm:flex-row justify-center gap-4">
            <a
              href="mailto:<EMAIL>"
              className="bg-gradient-to-r from-[#316bff] to-[#4f46e5] text-white px-8 py-4 rounded-xl font-bold text-lg hover:shadow-lg transform hover:scale-105 transition-all duration-300 flex items-center justify-center gap-2"
            >
              <Mail className="w-5 h-5" />
              Request Deployment
              <ArrowRight className="w-5 h-5" />
            </a>

            <Link href="/register">
              <button className="bg-gray-100 text-gray-900 px-8 py-4 rounded-xl font-bold text-lg hover:bg-gray-200 transition-all duration-300 flex items-center justify-center gap-2">
                <Zap className="w-5 h-5" />
                Join Fuse Network
              </button>
            </Link>
          </div>
        </div>
      </section>

      <CtaSection />

      {/* Blackjack Game Modal */}
      <Dialog open={isGameOpen} onOpenChange={setIsGameOpen}>
        <DialogContent className="max-w-6xl bg-green-800 border-yellow-600 p-0">
          <DialogHeader className="p-6 pb-0">
            <DialogTitle className="text-center text-2xl font-bold text-yellow-300">
              🦆 Fuse Gaming 🦆
            </DialogTitle>
          </DialogHeader>
          <div className="p-6">
            <BlackjackGame />
          </div>
        </DialogContent>
      </Dialog>

      {/* Cookie Notice */}
      <CookieNotice />
    </>
  )
}
